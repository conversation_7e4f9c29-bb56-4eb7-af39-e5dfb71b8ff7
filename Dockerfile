FROM python:3.11-slim

# Ustaw katalog roboczy
WORKDIR /app

# Zainstaluj zależności systemowe
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Skopiuj requirements i zainstaluj zależności Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Skopiuj kod aplikacji
COPY . .

# Utw<PERSON>rz użytkownika bez uprawnień root
RUN useradd --create-home --shell /bin/bash celery_user
RUN chown -R celery_user:celery_user /app
USER celery_user

# Domyślna komenda
CMD ["python", "main.py"]
