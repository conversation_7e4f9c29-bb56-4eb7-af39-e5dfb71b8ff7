# Celery Demo Project

Demo projekt pokazujący użycie Celery z Flower dashboard i Docker Compose.

## 🚀 Funkcjonalności

- **Celery Workers** - przetwarzanie zadań w tle
- **Flower Dashboard** - monitorowanie zadań w czasie rzeczywistym
- **Redis** - broker i backend dla Celery
- **Automatyczny scheduler** - tworzy zadania co 10-30 sekund
- **Różne typy zadań**:
  - Długotrwałe zadania z aktualizacją postępu
  - Przetwarzanie danych
  - Proste obliczenia

## 📋 Wymagania

- Docker
- Docker Compose

## 🛠️ Instalacja i uruchomienie

### 1. Klonowanie i uruchomienie

```bash
# Uruchom wszystkie serwisy
docker-compose up --build

# Lub w tle
docker-compose up --build -d
```

### 2. Dostęp do aplikacji

- **Flower Dashboard**: http://localhost:5555
  - Login: `admin`
  - <PERSON><PERSON><PERSON>: `password`

### 3. <PERSON><PERSON><PERSON><PERSON><PERSON>

```bash
docker-compose down
```

## 📊 Monitorowanie

### Flower Dashboard

Flower dostępny pod adresem http://localhost:5555 pozwala na:

- Monitorowanie aktywnych zadań
- Przeglądanie historii zadań
- Sprawdzanie statusu workerów
- Analizę wydajności
- Zarządzanie zadaniami (anulowanie, restart)

### Logi

```bash
# Wszystkie serwisy
docker-compose logs -f

# Konkretny serwis
docker-compose logs -f celery_worker
docker-compose logs -f app
docker-compose logs -f flower
```

## 🔧 Struktura projektu

```
CeleryDemo/
├── celery_app.py      # Konfiguracja Celery
├── tasks.py           # Definicje zadań
├── main.py            # Główna aplikacja (scheduler)
├── requirements.txt   # Zależności Python
├── Dockerfile         # Obraz Docker
├── docker-compose.yml # Konfiguracja serwisów
├── .env              # Zmienne środowiskowe
└── README.md         # Dokumentacja
```

## 📝 Typy zadań

### 1. Długotrwałe zadania (`long_running_task`)
- Symulują pracę trwającą 15-45 sekund
- Aktualizują postęp co 5 sekund
- Pokazują szczegółowy status w Flower

### 2. Przetwarzanie danych (`data_processing_task`)
- Symulują przetwarzanie 500-2000 rekordów
- Przetwarzają po 100 rekordów na raz
- Pokazują postęp w procentach

### 3. Obliczenia (`simple_calculation_task`)
- Proste operacje matematyczne
- Losowe operandy i operacje
- Krótki czas wykonania (2-8 sekund)

## ⚙️ Konfiguracja

### Zmienne środowiskowe (.env)

```env
REDIS_URL=redis://redis:6379/0
FLOWER_PORT=5555
FLOWER_BASIC_AUTH=admin:password
```

### Dostosowanie schedulera

W pliku `main.py` możesz zmienić:

```python
# Interwał między zadaniami (sekundy)
scheduler.run(interval_min=10, interval_max=30)
```

## 🐛 Rozwiązywanie problemów

### Redis nie startuje
```bash
docker-compose logs redis
```

### Worker nie łączy się z Redis
```bash
docker-compose logs celery_worker
```

### Flower nie jest dostępny
```bash
docker-compose logs flower
```

## 🔄 Rozwój

### Uruchomienie lokalnie (bez Docker)

```bash
# Zainstaluj zależności
pip install -r requirements.txt

# Uruchom Redis lokalnie
redis-server

# W osobnych terminalach:
# 1. Worker
celery -A celery_app worker --loglevel=info

# 2. Flower
celery -A celery_app flower

# 3. Główna aplikacja
python main.py
```

### Dodawanie nowych zadań

1. Dodaj funkcję zadania w `tasks.py`
2. Zaimportuj w `main.py`
3. Dodaj do `create_random_task()`

## 📚 Przydatne komendy

```bash
# Restart konkretnego serwisu
docker-compose restart celery_worker

# Skalowanie workerów
docker-compose up --scale celery_worker=3

# Czyszczenie danych Redis
docker-compose exec redis redis-cli FLUSHALL
```
