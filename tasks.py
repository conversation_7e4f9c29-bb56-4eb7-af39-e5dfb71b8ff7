"""
Celery tasks definitions
"""
import time
import random
from celery import current_task
from celery_app import app


@app.task(bind=True)
def long_running_task(self, task_name, duration=None):
    """
    Symuluje długotrwałe zadanie z aktualizacją statusu
    
    Args:
        task_name (str): Nazwa zadania
        duration (int, optional): Czas trwania w sekundach. Jeśli None, losowy czas 10-60s
    """
    if duration is None:
        duration = random.randint(10, 60)
    
    print(f"Rozpoczynam zadanie: {task_name} (czas: {duration}s)")
    
    # Aktualizuj status na początku
    self.update_state(
        state='PROGRESS',
        meta={
            'current': 0,
            'total': duration,
            'status': f'Roz<PERSON>czynam {task_name}...',
            'task_name': task_name
        }
    )
    
    # Symuluj pracę z aktualizacją postępu
    for i in range(duration):
        time.sleep(1)
        
        # Co 5 sekund aktualizuj status
        if i % 5 == 0:
            progress = int((i / duration) * 100)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': i,
                    'total': duration,
                    'status': f'Przetwarzam {task_name}... ({progress}%)',
                    'task_name': task_name
                }
            )
            print(f"Zadanie {task_name}: {progress}% ukończone")
    
    # Zadanie ukończone
    result = {
        'task_name': task_name,
        'duration': duration,
        'status': 'Zadanie ukończone pomyślnie',
        'result': f'Wynik zadania {task_name} po {duration} sekundach'
    }
    
    print(f"Ukończono zadanie: {task_name}")
    return result


@app.task(bind=True)
def data_processing_task(self, data_size):
    """
    Symuluje przetwarzanie danych
    
    Args:
        data_size (int): Rozmiar danych do przetworzenia
    """
    print(f"Rozpoczynam przetwarzanie {data_size} rekordów")
    
    total_steps = data_size // 100  # Przetwarzamy po 100 rekordów na raz
    
    for step in range(total_steps):
        time.sleep(random.uniform(0.5, 2.0))  # Symuluj czas przetwarzania
        
        processed = (step + 1) * 100
        progress = int((processed / data_size) * 100)
        
        self.update_state(
            state='PROGRESS',
            meta={
                'current': processed,
                'total': data_size,
                'status': f'Przetworzono {processed}/{data_size} rekordów',
                'progress': progress
            }
        )
        
        if step % 5 == 0:  # Log co 5 kroków
            print(f"Przetworzono {processed}/{data_size} rekordów ({progress}%)")
    
    result = {
        'processed_records': data_size,
        'status': 'Przetwarzanie ukończone',
        'summary': f'Pomyślnie przetworzono {data_size} rekordów'
    }
    
    print(f"Ukończono przetwarzanie {data_size} rekordów")
    return result


@app.task
def simple_calculation_task(x, y, operation='add'):
    """
    Proste zadanie obliczeniowe
    
    Args:
        x (float): Pierwsza liczba
        y (float): Druga liczba
        operation (str): Operacja do wykonania ('add', 'multiply', 'divide')
    """
    print(f"Wykonuję operację {operation} na {x} i {y}")
    
    # Symuluj czas obliczeń
    time.sleep(random.uniform(2, 8))
    
    if operation == 'add':
        result = x + y
    elif operation == 'multiply':
        result = x * y
    elif operation == 'divide':
        if y == 0:
            raise ValueError("Nie można dzielić przez zero")
        result = x / y
    else:
        raise ValueError(f"Nieznana operacja: {operation}")
    
    print(f"Wynik operacji {operation}({x}, {y}) = {result}")
    return {
        'operation': operation,
        'operands': [x, y],
        'result': result
    }
