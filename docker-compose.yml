version: '3.8'

services:
  # Redis - broker i backend dla Celery
  redis:
    image: redis:7-alpine
    container_name: celery_demo_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Celery Worker
  celery_worker:
    build: .
    container_name: celery_demo_worker
    command: celery -A celery_app worker --loglevel=info --concurrency=4
    volumes:
      - .:/app
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped

  # Flower - Dashboard dla Celery
  flower:
    build: .
    container_name: celery_demo_flower
    command: celery -A celery_app flower --port=5555 --basic_auth=admin:password
    ports:
      - "5555:5555"
    volumes:
      - .:/app
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped

  # Główna aplikacja - scheduler zadań
  app:
    build: .
    container_name: celery_demo_app
    command: python main.py
    volumes:
      - .:/app
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped

volumes:
  redis_data:
