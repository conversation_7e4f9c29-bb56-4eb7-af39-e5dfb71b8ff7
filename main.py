"""
Główna aplikacja - tworzy zadania w pętli
"""
import time
import random
import signal
import sys
from datetime import datetime
from tasks import long_running_task, data_processing_task, simple_calculation_task


class TaskScheduler:
    """Klasa odpowiedzialna za planowanie i tworzenie zadań"""
    
    def __init__(self):
        self.running = True
        self.task_counter = 0
        
        # Obsługa sygnałów dla graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Obsługa sygnałów zakończenia"""
        print(f"\nOtrzymano sygnał {signum}. Zatrzymuję scheduler...")
        self.running = False
    
    def create_random_task(self):
        """Tworzy losowe zadanie"""
        self.task_counter += 1
        task_types = ['long_running', 'data_processing', 'calculation']
        task_type = random.choice(task_types)
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if task_type == 'long_running':
            task_name = f"LongTask_{self.task_counter}"
            duration = random.randint(15, 45)
            
            print(f"[{timestamp}] Tworzę zadanie długotrwałe: {task_name} ({duration}s)")
            result = long_running_task.delay(task_name, duration)
            
        elif task_type == 'data_processing':
            data_size = random.randint(500, 2000)
            
            print(f"[{timestamp}] Tworzę zadanie przetwarzania danych: {data_size} rekordów")
            result = data_processing_task.delay(data_size)
            
        else:  # calculation
            x = random.uniform(1, 100)
            y = random.uniform(1, 100)
            operation = random.choice(['add', 'multiply', 'divide'])
            
            print(f"[{timestamp}] Tworzę zadanie obliczeniowe: {operation}({x:.2f}, {y:.2f})")
            result = simple_calculation_task.delay(x, y, operation)
        
        print(f"[{timestamp}] Zadanie utworzone z ID: {result.id}")
        return result
    
    def run(self, interval_min=10, interval_max=30):
        """
        Główna pętla schedulera
        
        Args:
            interval_min (int): Minimalny czas między zadaniami (sekundy)
            interval_max (int): Maksymalny czas między zadaniami (sekundy)
        """
        print("=" * 60)
        print("🚀 CELERY DEMO - TASK SCHEDULER")
        print("=" * 60)
        print(f"Scheduler uruchomiony. Interwał: {interval_min}-{interval_max}s")
        print("Naciśnij Ctrl+C aby zatrzymać")
        print("=" * 60)
        
        try:
            while self.running:
                # Utwórz nowe zadanie
                self.create_random_task()
                
                # Poczekaj losowy czas przed następnym zadaniem
                wait_time = random.randint(interval_min, interval_max)
                print(f"⏰ Następne zadanie za {wait_time} sekund...\n")
                
                # Poczekaj z możliwością przerwania
                for _ in range(wait_time):
                    if not self.running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print("\n🛑 Otrzymano Ctrl+C")
        
        print("✅ Scheduler zatrzymany")


def main():
    """Główna funkcja aplikacji"""
    scheduler = TaskScheduler()
    
    # Uruchom scheduler z domyślnymi ustawieniami
    # Zadania będą tworzone co 10-30 sekund
    scheduler.run(interval_min=10, interval_max=30)


if __name__ == "__main__":
    main()
